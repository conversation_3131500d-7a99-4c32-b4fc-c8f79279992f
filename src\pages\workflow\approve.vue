<route lang="json5">
{
  style: {
    navigationBarTitleText: '审批详情',
  },
}
</route>

<script lang="ts" setup>
import { ref, onMounted, watch, computed } from 'vue'
import {
  getWorkflowDetail,
  completeTask,
  terminationTask,
  completeTaskEnd,
} from '@/service/workflow'
import type {
  WorkflowDetailResponse,
  CompleteTaskRequest,
  TerminationTaskRequest,
  WorkflowProcessItem,
  ApprovalInfo,
} from '@/types/workflow'
import type { FlowStep } from '@/components/workflow/FlowStepItem.vue'
import { useMessage } from 'wot-design-uni'
import useUpload from '@/hooks/useUpload'
// import { useUserStore } from '@/store/user'

// 导入组件
import AttachmentList from '@/components/workflow/AttachmentList.vue'
import ApprovalProcess from '@/components/workflow/ApprovalProcess.vue'

// 获取当前用户信息（暂时保留，可能后续需要）
// const userStore = useUserStore()
// const currentUserId = computed(() => String(userStore.userInfo.username))

// 定义文件信息接口
interface FileInfo {
  url: string
  name: string
}

// MessageBox实例
const message = useMessage()
// 自定义同意模态框实例
const approveMessage = useMessage('wd-message-box-approve')
// 自定义拒绝模态框实例
const rejectMessage = useMessage('wd-message-box-reject')

// 获取页面参数
const id = ref<string | number>('')
const loading = ref(true)
const workflowData = ref<WorkflowDetailResponse | null>(null)
const flowSteps = ref<FlowStep[][]>([])

// 附件相关
const filesArray = ref<FileInfo[]>([])
const { loading: uploadLoading, data: uploadData, run: uploadFile } = useUpload<string>()

// 处理日期显示格式
const formatDate = (timestamp: number): string => {
  if (!timestamp) return ''
  const date = new Date(timestamp * 1000)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

// 获取工作流状态类型
const getStatusType = (status: number): string => {
  switch (status) {
    case 1:
      return 'success'
    case 2:
      return 'warning'
    case 3:
      return 'error'
    default:
      return 'warning'
  }
}

// 判断当前用户是否为当前激活的审批节点的审批人（暂时保留，可能后续需要）
// const isCurrentApprover = computed(() => {
//   if (!workflowData.value) return false

//   // 找到当前激活的审批节点（状态为0的第一个节点）
//   const activeNodes = workflowData.value.process.flatMap((branch) =>
//     branch.filter((item) => Number(item.status) === 0),
//   )

//   // 查找当前用户是否是其中一个审批人（处理多个审批人的情况）
//   return activeNodes.some((node) => {
//     // 如果user_id包含逗号，说明有多个审批人
//     if (node.user_id && node.user_id.includes(',')) {
//       const userIds = node.user_id.split(',').map((id) => id.trim())
//       return userIds.includes(currentUserId.value)
//     }
//     // 单个审批人的情况
//     return String(node.user_id) === currentUserId.value
//   })
// })

// 获取工作流详情数据
const fetchWorkflowDetail = async () => {
  if (!id.value) return

  try {
    loading.value = true
    const res = await getWorkflowDetail(id.value)
    workflowData.value = res
    updateFlowSteps()
    console.log('工作流详情:', res)
  } catch (error) {
    console.error('获取工作流详情失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 审批意见数据
const comments = ref([])

/**
 * 更新审批流程数据，处理多级流程分支
 */
const updateFlowSteps = () => {
  if (!workflowData.value) return

  // 检查整个工作流是否已驳回
  const isWorkflowRevoked = workflowData.value.form.status === 4

  // 将二维数组的每个分支转换为对应的流程步骤
  flowSteps.value = workflowData.value.process.map((branch) => {
    return branch.map((item, index) => {
      // 确定流程状态
      let status: 'success' | 'rejected' | 'active' | 'waiting' | 'revoked' | 'returned' = 'waiting'
      let isRevoked = false

      // 如果整个工作流已被驳回，标记所有节点为驳回状态
      if (isWorkflowRevoked) {
        status = 'revoked'
        isRevoked = true
      } else {
        // 处理节点状态：1-通过，2-拒绝，3-驳回，0-等待，4-驳回
        const itemStatus = Number(item.status)
        if (itemStatus === 1) {
          status = 'success' // 通过状态
        } else if (itemStatus === 2) {
          status = 'rejected' // 拒绝状态
        } else if (itemStatus === 3) {
          status = 'returned' // 驳回状态
        } else if (itemStatus === 4) {
          status = 'revoked' // 驳回状态
          isRevoked = true
        } else if (
          index === 0 ||
          (index > 0 && [1, 2, 3].includes(Number(branch[index - 1].status)))
        ) {
          status = 'active' // 激活状态（当前节点或前一个节点已完成）
        }
      }

      // 获取审批意见和评论信息
      const { comment, commentInfo, isRejected } = extractCommentInfo(item, isRevoked)

      return {
        id: item.id,
        name: item.name,
        handler: item.user_name,
        time: item.time || '等待处理',
        status,
        comment,
        commentInfo,
        isRejected,
        isRevoked,
      }
    })
  })
}

/**
 * 从流程项中提取评论信息
 */
const extractCommentInfo = (item: WorkflowProcessItem, isRevoked: boolean) => {
  let comment = ''
  let commentInfo = null
  let isRejected = false

  // 检查 info 数组中是否有审批意见
  if (item.info && Array.isArray(item.info) && item.info.length > 0) {
    // 获取第一条意见的内容
    const infoItem = item.info[0]
    if (infoItem.value && infoItem.value.trim() !== '') {
      comment = infoItem.value
      commentInfo = {
        user: infoItem.name,
        time: infoItem.time,
        content: infoItem.value,
        status: infoItem.status, // 保存意见状态：1-通过，2-拒绝，3-驳回
      }
      // 标记是否是拒绝意见
      isRejected = infoItem.status === 2 || infoItem.status === 3
    }
  }

  // 如果有驳回备注，使用作为意见
  if (item.status === 4 && item.remark) {
    comment = item.remark
    commentInfo = {
      user: item.user_name || '',
      time: item.time || '',
      content: item.remark,
      status: 4, // 驳回状态
    }
  }

  return {
    comment,
    commentInfo,
    isRejected,
  }
}

// 获取节点配置信息
const getNodeConfig = (configStr: string) => {
  try {
    return JSON.parse(configStr || '{}')
  } catch (error) {
    console.error('解析节点配置失败:', error)
    return {}
  }
}

// 审批意见输入框内容
const commentInput = ref('')

// 提交意见
const submitComment = () => {
  if (!commentInput.value.trim()) return

  comments.value.push({
    id: comments.value.length + 1,
    user: '当前用户', // 实际应用中应该是从登录信息获取
    time: new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    }),
    content: commentInput.value,
  })

  commentInput.value = ''
}

// 查看附件
const previewAttachment = (url: string) => {
  uni.previewImage({
    urls: [url],
    current: url,
  })
}

// 上传附件
const handleUploadAttachment = () => {
  uploadFile()
}

// 删除已上传的附件
const removeFile = (index: number) => {
  filesArray.value.splice(index, 1)
}

// 监听上传响应数据
watch(uploadData, (newData) => {
  if (newData) {
    try {
      // 尝试解析上传响应，根据实际接口返回格式可能需要调整
      const response = typeof newData === 'string' ? JSON.parse(newData) : newData
      if (response.code === 1 && response.data) {
        // 保存文件URL和文件名
        const fileInfo: FileInfo = {
          url: response.data.file.full_url,
          name: response.data.file.name,
        }
        filesArray.value.push(fileInfo)
        uni.showToast({
          title: '附件上传成功',
          icon: 'success',
        })
      } else {
        uni.showToast({
          title: response.msg || '附件上传失败',
          icon: 'none',
        })
      }
    } catch (error) {
      console.error('解析上传结果失败:', error)
      uni.showToast({
        title: '附件上传失败',
        icon: 'none',
      })
    }
  }
})

// 模态框中的数据
const modalComment = ref('')
const modalUploading = ref(false)

// 拒绝模态框数据
const rejectComment = ref('')
const rejectUploading = ref(false)
const rejectFilesArray = ref<FileInfo[]>([])

// 审批操作
const handleApprove = (approve: boolean) => {
  if (!workflowData.value || !workflowData.value.node) {
    uni.showToast({
      title: '获取审批数据失败',
      icon: 'none',
    })
    return
  }

  if (approve) {
    // 同意审批 - 弹出模态框
    modalComment.value = '' // 清空之前的评论
    approveMessage
      .confirm({
        title: '审批同意',
        confirmButtonText: '确认同意',
        cancelButtonText: '取消',
      })
      .then(() => {
        // 用户点击确认
        if (!modalComment.value.trim()) {
          uni.showToast({
            title: '请输入审批意见',
            icon: 'none',
          })
          return
        }

        // 调用同意接口
        loading.value = true
        const params: CompleteTaskRequest = {
          taskId: Number(workflowData.value?.node.id || 0),
          message: modalComment.value,
          file: filesArray.value.map((file) => file.url).join(','), // 将文件URL数组转换为逗号分隔的字符串
          taskVariables: {},
          variables: {},
          status: 1,
        }

        completeTask(params)
          .then((res) => {
            console.log(res)
            // 无论completeTaskEnd成功还是失败，都显示审批成功并关闭loading
            loading.value = false
            filesArray.value = [] // 清空文件列表
            fetchWorkflowDetail()
            uni.showToast({
              title: '审批成功',
              icon: 'success',
            })
            /*  // 审批成功后刷新页面
            completeTaskEnd(params)
              .then(() => {
                console.log('completeTaskEnd执行完成')
              })
              .catch((endError) => {
                console.error('completeTaskEnd执行失败:', endError)
              }) */
          })
          .catch((error) => {
            console.error(error)
            loading.value = false
            uni.showToast({
              title: error.msg || '审批失败',
              icon: 'none',
            })
          })
      })
      .catch(() => {
        // 用户取消操作，不做任何处理
        console.log('用户取消同意操作')
      })
  } else {
    // 拒绝审批 - 弹出自定义拒绝模态框
    rejectComment.value = '' // 清空之前的评论
    rejectFilesArray.value = [] // 清空文件列表
    rejectMessage
      .confirm({
        title: '拒绝审批',
        confirmButtonText: '确认拒绝',
        cancelButtonText: '取消',
      })
      .then(() => {
        // 用户点击确认
        if (!rejectComment.value.trim()) {
          uni.showToast({
            title: '请输入拒绝原因',
            icon: 'none',
          })
          return
        }

        // 调用拒绝接口
        loading.value = true
        const params: TerminationTaskRequest = {
          taskId: Number(workflowData.value?.node.id || 0),
          message: rejectComment.value,
          status: 2,
          // 如果需要支持附件，可以像同意接口一样添加file字段
          // file: rejectFilesArray.value.map((file) => file.url).join(',')
        }

        terminationTask(params)
          .then((res) => {
            uni.showToast({
              title: res.msg || '已拒绝申请',
              icon: 'success',
            })
            /* // 添加completeTaskEnd调用，与同意审批保持一致
            completeTaskEnd({
              taskId: Number(workflowData.value?.node.id || 0),
              message: rejectComment.value,
              status: 2,
            })
              .then(() => {
                console.log('completeTaskEnd执行完成')
              })
              .catch((endError) => {
                console.error('completeTaskEnd执行失败:', endError)
              }) */
            // 审批成功后刷新页面
            setTimeout(() => {
              fetchWorkflowDetail()
            }, 1500)
          })
          .catch((error) => {
            uni.showToast({
              title: error.msg || '操作失败',
              icon: 'none',
            })
          })
          .finally(() => {
            loading.value = false
          })
      })
      .catch(() => {
        // 用户取消操作，不做任何处理
        console.log('用户取消拒绝操作')
      })
  }
}

// 模态框中上传文件
const modalUploadFile = () => {
  modalUploading.value = true
  uploadFile()
}

// 监听模态框中的上传状态
watch(uploadLoading, (newLoading) => {
  modalUploading.value = newLoading
})

// 获取状态标签样式
const getStatusClass = (type: string): string => {
  switch (type) {
    case 'success':
      return 'bg-green-500'
    case 'warning':
      return 'bg-orange-500'
    case 'error':
      return 'bg-red-500'
    default:
      return 'bg-gray-500'
  }
}

// 页面加载时获取数据
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.$page?.options || {}

  if (options.id) {
    id.value = options.id
    fetchWorkflowDetail()
  } else {
    uni.showToast({
      title: '参数错误',
      icon: 'none',
    })
  }
})
</script>

<template>
  <view class="workflow-approve bg-gray-100 min-h-screen">
    <!-- MessageBox组件 -->
    <!--    <wd-message-box></wd-message-box>-->

    <!-- 自定义审批同意弹框 -->
    <wd-message-box selector="wd-message-box-approve">
      <view class="approve-form p-2">
        <!-- 审批意见 -->
        <view class="mb-4">
          <view class="text-sm text-gray-500 mb-2">审批意见 (必填)</view>
          <textarea
            v-model="modalComment"
            placeholder="请输入审批意见..."
            class="w-full custom-textarea approve-textarea"
            :maxlength="200"
            auto-height
          ></textarea>
          <view class="text-xs text-gray-400 text-right mt-1">{{ modalComment.length }}/200</view>
        </view>

        <!-- 上传附件 -->
        <view class="mb-4">
          <view class="flex items-center mb-2">
            <wd-icon name="attach" class="text-gray-500 mr-1" />
            <text class="text-sm text-gray-500">附件 (可选)</text>
          </view>

          <view class="flex flex-col">
            <wd-button
              size="small"
              type="info"
              plain
              @click="modalUploadFile"
              :loading="modalUploading"
              class="self-start mb-2"
            >
              <view class="flex items-center">
                <wd-icon name="upload" class="mr-1" />
                上传附件
              </view>
            </wd-button>
            <text class="text-xs text-gray-500 break-words">
              支持上传不超过20MB的png/jpg/jpeg/doc/docx/xlsx/xls/ppt/pdf格式文件
            </text>
          </view>

          <!-- 显示已上传的多个附件 -->
          <view v-if="filesArray.length > 0" class="mt-2">
            <view
              v-for="(file, index) in filesArray"
              :key="index"
              class="mt-2 p-2 bg-gray-50 rounded flex items-center"
            >
              <wd-icon name="file-icon" class="mr-2 text-blue-500 flex-shrink-0" />
              <text class="text-sm text-gray-700 flex-1 truncate">
                {{ file.name }}
              </text>
              <wd-icon
                name="close"
                class="text-gray-500 flex-shrink-0"
                @click="removeFile(index)"
              />
            </view>
          </view>
        </view>
      </view>
    </wd-message-box>

    <!-- 自定义审批拒绝弹框 -->
    <wd-message-box selector="wd-message-box-reject">
      <view class="reject-form p-2">
        <!-- 拒绝原因 -->
        <view class="mb-4">
          <view class="text-sm text-gray-500 mb-2">拒绝原因 (必填)</view>
          <textarea
            v-model="rejectComment"
            placeholder="请输入拒绝原因..."
            class="w-full custom-textarea"
            :maxlength="200"
            auto-height
          ></textarea>
          <view class="text-xs text-gray-400 text-right mt-1">{{ rejectComment.length }}/200</view>
        </view>

        <!-- 拒绝也可以添加上传附件功能，但这里暂时注释掉，如果需要可以取消注释 -->
        <!--
        <view class="mb-4">
          <view class="flex items-center mb-2">
            <wd-icon name="attach" class="text-gray-500 mr-1" />
            <text class="text-sm text-gray-500">附件 (可选)</text>
          </view>

          <view class="flex flex-col">
            <wd-button
              size="small"
              type="info"
              plain
              @click="rejectUploadFile"
              :loading="rejectUploading"
              class="self-start mb-2"
            >
              <view class="flex items-center">
                <wd-icon name="upload" class="mr-1" />
                上传附件
              </view>
            </wd-button>
            <text class="text-xs text-gray-500 break-words">
              支持上传不超过20MB的png/jpg/jpeg/doc/docx/xlsx/xls/ppt/pdf格式文件
            </text>
          </view>

          <view v-if="rejectFilesArray.length > 0" class="mt-2">
            <view
              v-for="(file, index) in rejectFilesArray"
              :key="index"
              class="mt-2 p-2 bg-gray-50 rounded flex items-center"
            >
              <wd-icon name="file-icon" class="mr-2 text-blue-500 flex-shrink-0" />
              <text class="text-sm text-gray-700 flex-1 truncate">
                {{ file.name }}
              </text>
              <wd-icon
                name="close"
                class="text-gray-500 flex-shrink-0"
                @click="rejectFilesArray.splice(index, 1)"
              />
            </view>
          </view>
        </view>
        -->
      </view>
    </wd-message-box>

    <wd-loading text="加载中..." :mask="true" v-if="loading || uploadLoading" />

    <block v-if="workflowData && !loading">
      <!-- 内容区域，底部padding根据审批按钮是否显示来动态调整 -->
      <view
        class="page-content px-4 py-4"
        :class="{
          'pb-24': workflowData.form.isApprove === false,
          'pb-4': workflowData.form.isApprove !== false,
        }"
      >
        <!-- 审批头部信息 -->
        <view class="detail-card bg-white rounded-lg shadow mb-4 p-4">
          <view class="flex justify-between items-center mb-2">
            <view class="text-lg font-bold text-gray-800">{{ workflowData.form.title }}</view>
            <view
              :class="[
                'status-tag px-2 py-0.5 text-sm text-white rounded',
                getStatusClass(getStatusType(workflowData.form.status)),
              ]"
            >
              {{
                workflowData.form.status === 1
                  ? '已审批'
                  : workflowData.form.status === 0
                    ? '待审批'
                    : '已拒绝'
              }}
            </view>
          </view>
          <view class="text-sm text-gray-500 mb-4">
            申请编号：{{ workflowData.node.id || '-' }}
          </view>

          <view class="border-t border-gray-100 pt-3">
            <!-- 显示表单数据，过滤掉fjlb字段和有附件时的file字段 -->
            <view
              class="info-item mb-3 text-base"
              v-for="field in workflowData.fields && Array.isArray(workflowData.fields)
                ? workflowData.fields.filter((f) => {
                    if (f.show !== '1') return false
                    if (f.name === 'fjlb') return false
                    // 如果有附件（zp、fjlb或file），则隐藏file字段
                    if (
                      f.name === 'file' &&
                      (workflowData.formData.zp ||
                        workflowData.formData.fjlb ||
                        workflowData.formData.file)
                    ) {
                      return false
                    }
                    return true
                  })
                : []"
              :key="field.name"
            >
              <view class="info-label text-gray-500 mb-1">{{ field.label }}：</view>

              <!-- 根据字段类型渲染不同的展示方式 -->
              <view v-if="field.type === 'text'" class="info-value text-gray-800 break-all">
                {{ workflowData.formData[field.name] || '-' }}
              </view>

              <!-- 表格类型展示 -->
              <view
                v-else-if="
                  field.type === 'table' && workflowData.cols && workflowData.cols[field.name]
                "
                class="table-container mt-2"
              >
                <view class="table-responsive">
                  <!-- 表头 -->
                  <view class="table-header flex border-b border-gray-200">
                    <view
                      v-for="column in workflowData.cols[field.name]"
                      :key="column.key"
                      class="table-cell py-2 px-3 text-sm font-medium text-gray-700 bg-gray-50"
                      :style="
                        column.width
                          ? {
                              width: column.width + 'px',
                              minWidth: (column.minWidth || column.width) + 'px',
                            }
                          : {}
                      "
                    >
                      {{ column.title }}
                    </view>
                  </view>

                  <!-- 表格内容 -->
                  <view
                    v-if="
                      workflowData.formData[field.name] &&
                      workflowData.formData[field.name].length > 0
                    "
                    class="table-body"
                  >
                    <view
                      v-for="(row, rowIndex) in workflowData.formData[field.name]"
                      :key="rowIndex"
                      class="table-row flex border-b border-gray-200 hover:bg-gray-50"
                    >
                      <view
                        v-for="column in workflowData.cols[field.name]"
                        :key="column.key"
                        class="table-cell py-2 px-3 text-sm text-gray-800"
                        :style="
                          column.width
                            ? {
                                width: column.width + 'px',
                                minWidth: (column.minWidth || column.width) + 'px',
                              }
                            : {}
                        "
                      >
                        {{ row[column.dataIndex] || '-' }}
                      </view>
                    </view>
                  </view>

                  <!-- 无数据展示 -->
                  <view v-else class="empty-data py-4 text-center text-gray-500 text-sm">
                    暂无数据
                  </view>
                </view>
              </view>

              <!-- 多行文本类型展示 -->
              <view
                v-else-if="field.type === 'row'"
                class="info-value text-gray-800 break-all whitespace-pre-wrap"
              >
                {{ workflowData.formData[field.name] || '-' }}
              </view>

              <!-- 默认展示方式 -->
              <view v-else class="info-value text-gray-800 break-all">
                {{ workflowData.formData[field.name] || '-' }}
              </view>
            </view>
          </view>
        </view>

        <!-- 访问事由 -->
        <view
          class="detail-card bg-white rounded-lg shadow mb-4 p-4"
          v-if="workflowData.formData && workflowData.formData.dfsy"
        >
          <view class="card-title flex items-center mb-3">
            <view class="title-bar w-1.5 h-8 bg-blue-500 rounded mr-2"></view>
            <text class="text-base font-bold text-gray-800">访问事由</text>
          </view>
          <view class="text-sm text-gray-800 leading-relaxed">
            {{ workflowData.formData.dfsy }}
          </view>
        </view>

        <!-- 附件 -->
        <AttachmentList
          :photo-url="workflowData.formData.zp"
          :attachment-urls="workflowData.formData.fjlb"
          :file-urls="workflowData.formData.file"
        />

        <!-- 审批流程 -->
        <ApprovalProcess :workflow-data="workflowData" :flow-steps="flowSteps" />
      </view>

      <!-- 固定在底部的审批按钮 - 根据form.isApprove控制显示 -->
      <view
        class="approval-actions fixed left-0 right-0 bottom-0 bg-white shadow-md p-4 z-10 flex justify-between"
        v-if="workflowData && workflowData.form && workflowData.form.isApprove === true"
      >
        <wd-button type="info" plain class="flex-1 mr-4" @click="handleApprove(false)">
          拒绝
        </wd-button>
        <wd-button type="primary" class="flex-1" @click="handleApprove(true)">同意</wd-button>
      </view>
    </block>

    <!-- 无数据显示 -->
    <view class="flex flex-col items-center justify-center py-10" v-if="!loading && !workflowData">
      <wd-icon name="info-circle" size="50px" color="#c0c4cc"></wd-icon>
      <view class="text-gray-500 mt-4">暂无数据</view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
/* 主容器样式 */
.workflow-approve {
  min-height: 100vh;
}
/* 卡片样式 */
.detail-card {
  margin-bottom: 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
/* 标题左侧竖条 */
.title-bar {
  border-radius: 3rpx;
}
/* 状态标签 */
.status-tag {
  line-height: 1.2;
}
/* 表单字段 */
.info-item {
  width: 100%;
}
/* 表单标签 */
.info-label {
  font-weight: 500;
}
/* 表单值 */
.info-value {
  word-break: break-all;
  word-wrap: break-word;
  white-space: normal;
}
/* 审批操作按钮容器 */
.approval-actions {
  padding-bottom: calc(constant(safe-area-inset-bottom) + 32rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 32rpx);
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.05);
}
/* 同意弹窗表单样式 */
.approve-form {
  box-sizing: border-box;
  width: 100%;
  max-height: 70vh;
  overflow-y: auto;
  word-break: break-word;
}
/* 弹窗样式优化 - 增加响应式适配 */
:deep(.wd-message-box__container) {
  box-sizing: border-box;
  width: 95vw; /* 默认宽度，小屏幕下占95%视口宽度 */
  min-width: 300rpx; /* 最小宽度，避免太窄 */
  max-width: 650rpx; /* 小屏幕的最大宽度 */
}
/* 针对中等屏幕的样式 */
@media screen and (min-width: 768px) {
  :deep(.wd-message-box__container) {
    width: 80vw;
    max-width: 800rpx;
  }
}
/* 针对大屏幕的样式 */
@media screen and (min-width: 1024px) {
  :deep(.wd-message-box__container) {
    width: 60vw;
    max-width: 1000rpx;
  }
}
/* 针对特大屏幕的样式 */
@media screen and (min-width: 1440px) {
  :deep(.wd-message-box__container) {
    width: 40vw;
    max-width: 1200rpx;
  }
}

:deep(.wd-message-box__content) {
  padding: 24rpx; /* 增加内容区域的内边距 */
  overflow-x: hidden;
}

:deep(.wd-message-box__body) {
  padding: 24rpx 24rpx 32rpx; /* 调整body内边距 */
}

:deep(.wd-message-box__header) {
  padding: 24rpx 24rpx 0; /* 调整标题区域内边距 */
}

:deep(.wd-message-box__title) {
  font-size: 32rpx; /* 增大标题字体 */
  font-weight: 600;
}

:deep(.wd-message-box__footer) {
  padding: 16rpx 24rpx 24rpx; /* 调整底部按钮区域内边距 */
}
/* 输入框样式调整，使其像textarea */
:deep(.wd-message-box__input) {
  min-height: 150rpx; /* 设置最小高度 */
  padding: 20rpx;
  margin-top: 16rpx; /* 增加顶部间距 */
  font-size: 28rpx;
  resize: vertical; /* 允许垂直调整大小 */
  background-color: #f5f7fa; /* 轻微的背景色 */
  border: 2rpx solid #dcdfe6; /* 添加明显的边框 */
  border-radius: 8rpx; /* 圆角边框 */
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05) inset; /* 内阴影效果 */
}
/* 添加输入框焦点效果 */
:deep(.wd-message-box__input:focus) {
  border-color: #3b82f6; /* 聚焦时边框颜色 */
  outline: none;
  box-shadow: 0 0 0 2rpx rgba(59, 130, 246, 0.3); /* 聚焦时阴影效果 */
}
/* 折叠/展开按钮样式 */
.cursor-pointer {
  cursor: pointer;
}
/* 添加折叠区域的过渡效果 */
.relative.py-2 {
  transition: all 0.3s ease;
}
/* 自定义textarea样式 */
.custom-textarea {
  box-sizing: border-box;
  width: 100%;
  min-height: 150rpx;
  max-height: 300rpx; /* 限制最大高度 */
  padding: 20rpx;
  margin-top: 8rpx;
  overflow-y: auto; /* 内容过多时显示滚动条 */
  font-size: 28rpx;
  line-height: 1.5;
  word-break: break-word;
  word-wrap: break-word;
  white-space: pre-wrap; /* 保留换行符并自动换行 */
  resize: none; /* 移动端禁用resize */
  background-color: #ffffff; /* 修改为白色背景 */
  border: 2rpx solid #dcdfe6; /* 保持灰色边框 */
  border-radius: 8rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.03); /* 轻微的阴影效果 */
}
/* 自定义textarea焦点样式 */
.custom-textarea:focus {
  border-color: #ef4444; /* 拒绝时使用红色 */
  outline: none;
  box-shadow: 0 0 0 2rpx rgba(239, 68, 68, 0.2); /* 减轻阴影效果 */
}
/* 自定义textarea占位符颜色 */
.custom-textarea::placeholder {
  color: #c0c4cc;
}
/* 同意输入框特殊样式 - 默认高度更矮 */
.approve-textarea {
  min-height: 100rpx; /* 比拒绝输入框默认矮 */
  max-height: 250rpx; /* 最大高度也略小一些 */
}
/* 同意输入框焦点样式 - 使用蓝色而非红色 */
.approve-textarea:focus {
  border-color: #3b82f6; /* 同意时使用蓝色 */
  box-shadow: 0 0 0 2rpx rgba(59, 130, 246, 0.2); /* 蓝色阴影效果 */
}
/* 拒绝表单样式 */
.reject-form {
  box-sizing: border-box;
  width: 100%;
  max-height: 60vh; /* 减少最大高度，避免超出屏幕 */
  overflow-y: auto;
  word-break: break-word;
}
/* 表格样式 */
.table-container {
  width: 100%;
  overflow-x: auto;
  border: 1px solid #ebeef5;
  border-radius: 8rpx;
}

.table-responsive {
  width: fit-content;
  min-width: 100%;
}

.table-header {
  background-color: #f8fafc;
}

.table-cell {
  flex: 1;
  align-items: center;
  min-width: 120rpx;
  word-break: break-all;
}

.table-row:nth-child(even) {
  background-color: #fafafa;
}

.table-row:hover {
  background-color: #f5f7fa;
}

.empty-data {
  background-color: #fafafa;
}
</style>
