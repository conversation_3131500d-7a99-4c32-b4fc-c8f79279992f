<route lang="json5">
{
  style: {
    navigationBarTitleText: '非教学场地使用申请表单',
  },
}
</route>
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import dayjs from 'dayjs'
import { useUserStore } from '@/store/user'
import { getSectionListApi } from '@/service/section'
import type { SectionOption } from '@/types/section'
import FormContainer from '@/components/common/FormContainer.vue'
import FormItem from '@/components/common/FormItem.vue'
import FormLabel from '@/components/common/FormLabel.vue'
import TextPlaceholder from '@/components/common/TextPlaceholder.vue'
import TextareaInput from '@/components/common/TextareaInput.vue'
import OptionSelector from '@/components/common/OptionSelector.vue'
import SectionSelector from '../components/SectionSelector.vue'

// 获取用户信息
const userStore = useUserStore()

// 表单数据
const formData = ref({
  usageTheme: '', // 使用主题
  usageDate: dayjs().format('YYYY-MM-DD'), // 使用日期（默认今天）
  period: '', // 使用节次
  weekDay: '', // 星期
  remark: '', // 备注
  deadlineDate: '', // 截止日期
  deadlineWeekDay: '', // 截止日期的星期
  jc: '', // 节次值（用于提交）
  jcshow: '', // 节次显示值
})

// 节次选项数据
const selectOptions = ref<SectionOption[]>([])
const jcShowArr = ref<string[]>([])
const jcArr = ref<string[]>([])

// 弹窗控制
const showUsageDatePicker = ref(false)
const showDeadlineDatePicker = ref(false)

// 临时日期值（用于日期选择器）
const tempUsageDate = ref(new Date().getTime())
const tempDeadlineDate = ref(new Date().getTime())

// 计算属性：使用部门（从用户信息读取，只读）
const usageDepartment = computed(() => {
  return userStore.userInfo.department || '物联网与人工智能学院'
})

// 计算属性：负责人（从用户信息读取，只读）
const responsiblePerson = computed(() => {
  return userStore.userInfo.realname || '余新华'
})

// 计算属性：当前选中的节次显示文本
const selectedPeriodText = computed(() => {
  if (selectedPeriodIndex.value >= 0 && selectOptions.value[selectedPeriodIndex.value]) {
    return selectOptions.value[selectedPeriodIndex.value].label
  }
  return ''
})

// 星期选项数据
const weekList = [
  {
    label: '周一',
    value: '1',
  },
  {
    label: '周二',
    value: '2',
  },
  {
    label: '周三',
    value: '3',
  },
  {
    label: '周四',
    value: '4',
  },
  {
    label: '周五',
    value: '5',
  },
  {
    label: '周六',
    value: '6',
  },
  {
    label: '周日',
    value: '7',
  },
]

// 星期选项（转换为字符串数组供OptionSelector使用）
const weekDayOptions = weekList.map((item) => item.label)
const weekDayValues = weekList.map((item) => item.value)

// 选中的索引
const selectedPeriodIndex = ref(-1)
const selectedWeekDayIndex = ref(-1)
const selectedDeadlineWeekDayIndex = ref(-1)

// 处理使用主题输入
const handleThemeInput = (value: string) => {
  formData.value.usageTheme = value
}

// 处理备注输入
const handleRemarkInput = (value: string) => {
  formData.value.remark = value
}

// 日期格式化函数
const formatDate = (timestamp: number) => {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 处理使用日期选择
const openUsageDatePicker = () => {
  if (formData.value.usageDate) {
    tempUsageDate.value = new Date(formData.value.usageDate).getTime()
  }
  showUsageDatePicker.value = true
}

const confirmUsageDate = () => {
  formData.value.usageDate = formatDate(tempUsageDate.value)
  showUsageDatePicker.value = false
  // 日期变化时重新检查节次可用性
  if (selectOptions.value.length > 0) {
    checkSectionList()
  }
}

const closeUsageDatePicker = () => {
  showUsageDatePicker.value = false
}

// 处理截止日期选择
const openDeadlineDatePicker = () => {
  if (formData.value.deadlineDate) {
    tempDeadlineDate.value = new Date(formData.value.deadlineDate).getTime()
  }
  showDeadlineDatePicker.value = true
}

const confirmDeadlineDate = () => {
  formData.value.deadlineDate = formatDate(tempDeadlineDate.value)
  showDeadlineDatePicker.value = false
}

const closeDeadlineDatePicker = () => {
  showDeadlineDatePicker.value = false
}

// 处理节次选择
const handlePeriodChange = (index: number) => {
  selectedPeriodIndex.value = index
  const selectedOption = selectOptions.value[index]
  if (selectedOption) {
    formData.value.period = selectedOption.label
    formData.value.jc = selectedOption.value
    formData.value.jcshow = selectedOption.value.split('|')[1]
    const arr = selectedOption.value.split('|')
    jcShowArr.value = arr[1].split('-')
    jcArr.value = arr[0].split('-')
  }
}

// 处理星期选择
const handleWeekDayChange = (index: number) => {
  selectedWeekDayIndex.value = index
  formData.value.weekDay = weekDayValues[index]
}

// 处理截止日期星期选择
const handleDeadlineWeekDayChange = (index: number) => {
  selectedDeadlineWeekDayIndex.value = index
  formData.value.deadlineWeekDay = weekDayValues[index]
}

// 获取当前选中的星期显示文本
const selectedWeekDayText = computed(() => {
  return selectedWeekDayIndex.value >= 0 ? weekDayOptions[selectedWeekDayIndex.value] : ''
})

// 获取当前选中的截止日期星期显示文本
const selectedDeadlineWeekDayText = computed(() => {
  return selectedDeadlineWeekDayIndex.value >= 0
    ? weekDayOptions[selectedDeadlineWeekDayIndex.value]
    : ''
})

// 检查节次列表（根据当前时间禁用已过时间的节次）
function checkSectionList() {
  if (formData.value.usageDate === dayjs().format('YYYY-MM-DD')) {
    const date = dayjs().format('YYYY-MM-DD HH:mm:ss')
    let firstAvailableIndex = -1

    selectOptions.value.forEach((a, index) => {
      let num = 0
      for (let i = 0; i < a.times.length; i++) {
        if (dayjs(`${formData.value.usageDate} ${a.times[i]}`).isBefore(dayjs(date))) {
          break
        }
        num++
      }
      a.disabled = num < a.times.length

      // 记录第一个可用的节次索引
      if (!a.disabled && firstAvailableIndex === -1) {
        firstAvailableIndex = index
      }
    })

    // 如果没有选中任何节次，自动选中第一个可用的节次
    if (!formData.value.jc && firstAvailableIndex !== -1) {
      const selectedOption = selectOptions.value[firstAvailableIndex]
      selectedPeriodIndex.value = firstAvailableIndex
      formData.value.period = selectedOption.label
      formData.value.jc = selectedOption.value
      formData.value.jcshow = selectedOption.value.split('|')[1]
      const arr = selectedOption.value.split('|')
      jcShowArr.value = arr[1].split('-')
      jcArr.value = arr[0].split('-')
    }
  } else {
    selectOptions.value.forEach((a) => {
      a.disabled = false
    })

    // 非今天的日期，如果没有选中任何节次，自动选中第一个节次
    if (!formData.value.jc && selectOptions.value.length > 0) {
      const selectedOption = selectOptions.value[0]
      selectedPeriodIndex.value = 0
      formData.value.period = selectedOption.label
      formData.value.jc = selectedOption.value
      formData.value.jcshow = selectedOption.value.split('|')[1]
      const arr = selectedOption.value.split('|')
      jcShowArr.value = arr[1].split('-')
      jcArr.value = arr[0].split('-')
    }
  }
}

// 生命周期：组件挂载时获取节次列表
onMounted(() => {
  getSectionListApi().then((res) => {
    selectOptions.value = res
    selectOptions.value.forEach((a) => {
      a.label = `${a.label}（${a.times[0]} 至 ${a.times.at(-1)}）`
    })
    if (formData.value.usageDate) {
      checkSectionList()
    }
  })
})

// 暴露表单数据给父组件
defineExpose({
  formData,
})
</script>

<template>
  <FormContainer>
    <!-- 使用部门 -->
    <FormItem>
      <FormLabel text="使用部门" />
      <TextPlaceholder :value="usageDepartment" />
    </FormItem>

    <!-- 使用主题 -->
    <FormItem>
      <FormLabel text="使用主题" />
      <TextareaInput
        v-model="formData.usageTheme"
        @update:modelValue="handleThemeInput"
        placeholder="请输入使用主题"
        :maxlength="200"
        :show-count="true"
        :auto-height="true"
      />
    </FormItem>

    <!-- 使用日期 -->
    <FormItem>
      <FormLabel text="使用日期" />
      <view
        class="form-input p-[16rpx] bg-gray-50 rounded-lg flex items-center justify-between cursor-pointer"
        @click="openUsageDatePicker"
      >
        <text class="text-sm">{{ formData.usageDate || '请选择使用日期' }}</text>
        <wd-icon name="calendar" custom-style="color: #999; font-size: 28rpx;" />
      </view>
    </FormItem>

    <!-- 使用节次 -->
    <FormItem>
      <FormLabel text="使用节次" />
      <view class="section-selector">
        <SectionSelector
          v-model="selectedPeriodIndex"
          :options="selectOptions"
          :selected-value="selectedPeriodText"
          placeholder="请选择节次"
          @update:modelValue="handlePeriodChange"
        />
        <!-- 显示禁用的节次提示 -->
        <view v-if="selectOptions.some((option) => option.disabled)" class="disabled-tip">
          <text class="text-xs text-gray-500">灰色节次表示已过时间，无法选择</text>
        </view>
      </view>
    </FormItem>

    <!-- 备注 -->
    <FormItem>
      <FormLabel text="备注" />
      <TextareaInput
        v-model="formData.remark"
        @update:modelValue="handleRemarkInput"
        placeholder="请输入备注信息"
        :maxlength="500"
        :show-count="true"
        :auto-height="true"
      />
    </FormItem>

    <!-- 负责人 -->
    <FormItem>
      <FormLabel text="负责人" />
      <TextPlaceholder :value="responsiblePerson" />
    </FormItem>

    <!-- 批量申请配置 -->
    <FormItem>
      <FormLabel text="批量申请配置" />
      <view class="batch-config p-3 border-gray-200 border border-solid rounded-lg">
        <!-- 截止日期 -->
        <view class="config-item mb-[16rpx]">
          <text class="config-label text-sm text-gray-600 mb-[8rpx] block">截止日期</text>
          <view
            class="form-input p-[12rpx] bg-gray-50 rounded-lg flex items-center justify-between cursor-pointer"
            @click="openDeadlineDatePicker"
          >
            <text class="text-sm">{{ formData.deadlineDate || '请选择截止日期' }}</text>
            <wd-icon name="calendar" custom-style="color: #999; font-size: 28rpx;" />
          </view>
        </view>

        <!-- 星期 -->
        <view class="config-item">
          <text class="config-label text-sm text-gray-600 mb-[8rpx] block">星期</text>
          <OptionSelector
            v-model="selectedDeadlineWeekDayIndex"
            :options="weekDayOptions"
            :selected-value="selectedDeadlineWeekDayText"
            placeholder="请选择星期"
            @update:modelValue="handleDeadlineWeekDayChange"
          />
        </view>
      </view>
    </FormItem>
  </FormContainer>

  <!-- 使用日期选择器弹窗 -->
  <wd-popup v-model="showUsageDatePicker" position="bottom" close-on-click-modal>
    <view class="popup-header">
      <view class="text-gray-500" @click="closeUsageDatePicker">取消</view>
      <view class="popup-title">选择使用日期</view>
      <view class="text-blue-500" @click="confirmUsageDate">确定</view>
    </view>
    <view class="calendar-container">
      <wd-calendar-view
        v-model="tempUsageDate"
        :panel-height="400"
        :first-day-of-week="1"
        show-current
      />
    </view>
  </wd-popup>

  <!-- 截止日期选择器弹窗 -->
  <wd-popup v-model="showDeadlineDatePicker" position="bottom" close-on-click-modal>
    <view class="popup-header">
      <view class="text-gray-500" @click="closeDeadlineDatePicker">取消</view>
      <view class="popup-title">选择截止日期</view>
      <view class="text-blue-500" @click="confirmDeadlineDate">确定</view>
    </view>
    <view class="calendar-container">
      <wd-calendar-view
        v-model="tempDeadlineDate"
        :panel-height="400"
        :first-day-of-week="1"
        show-current
      />
    </view>
  </wd-popup>
</template>

<style lang="scss" scoped>
// 申请表单组件样式

.batch-config {
  .config-item {
    .config-label {
      font-weight: 500;
    }
  }
}

// 节次选择器样式
.section-selector {
  .disabled-tip {
    padding: 8rpx 12rpx;
    margin-top: 8rpx;
    background-color: #f5f5f5;
    border-radius: 8rpx;
  }
}

// 弹窗样式
.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1px solid #f0f0f0;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.calendar-container {
  padding: 0 32rpx 32rpx;
}
</style>
